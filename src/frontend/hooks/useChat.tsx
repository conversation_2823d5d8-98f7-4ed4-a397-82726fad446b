import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react'
import { MessageProps } from '../components/Message'

interface DelayedMessage {
  character: string
  text: string
  delay: number
}

interface ChatMessage {
  type: string
  sessionId?: string
  theme?: string
  character?: string
  text?: string
  message?: string
  error?: string
  details?: string
  skills?: string[]
}

interface ChatContextType {
  messages: MessageProps[]
  skills: string[]
  connectionStatus: string
  isConnected: boolean
  isStreaming: boolean
  isTyping: boolean
  sendMessage: (text: string) => void
}

const ChatContext = createContext<ChatContextType | undefined>(undefined)

export const useChat = () => {
  const context = useContext(ChatContext)
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider')
  }
  return context
}

interface ChatProviderProps {
  children: ReactNode
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const [ws, setWs] = useState<WebSocket | null>(null)
  const [messages, setMessages] = useState<MessageProps[]>([])
  const [skills, setSkills] = useState<string[]>([])
  const [connectionStatus, setConnectionStatus] = useState('Connecting...')
  const [isConnected, setIsConnected] = useState(false)
  const [isStreaming, setIsStreaming] = useState(false)
  const [isTyping, setIsTyping] = useState(false)

  const connect = useCallback(() => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const wsUrl = `${protocol}//${window.location.host}`
    
    const websocket = new WebSocket(wsUrl)
    
    websocket.onopen = () => {
      setConnectionStatus('Connected')
      setIsConnected(true)
      console.log('WebSocket connected')
    }
    
    websocket.onmessage = (event) => {
      const message: ChatMessage = JSON.parse(event.data)
      handleMessage(message)
    }
    
    websocket.onclose = () => {
      setConnectionStatus('Disconnected')
      setIsConnected(false)
      setWs(null)
      setTimeout(connect, 3000)
    }
    
    websocket.onerror = (error) => {
      console.error('WebSocket error:', error)
      setConnectionStatus('Connection Error')
      setIsConnected(false)
    }
    
    setWs(websocket)
  }, [])

  const handleMessage = useCallback((message: ChatMessage) => {
    const now = new Date()
    const timestamp = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })

    switch (message.type) {
      case 'connected':
        console.log('Connected with session ID:', message.sessionId)
        break
        
      case 'chat_start':
        setIsStreaming(true)
        if (message.theme) {
          addMessage('system', 'System', `--- ${message.theme} ---`)
        }
        break
        
      case 'message':
        if (message.character && message.text) {
          addMessage('assistant', message.character, message.text, timestamp)
        }
        break
        
      case 'autonomous_message':
        if (message.character && message.text) {
          addMessage('assistant', message.character, message.text, timestamp)
        }
        break
        
      case 'chat_complete':
        setIsStreaming(false)
        setIsTyping(false)
        if (message.skills && message.skills.length > 0) {
          setSkills(message.skills)
        }
        break
        
      case 'interrupted':
        if (message.message) {
          addMessage('system', 'System', message.message)
        }
        break
        
      case 'error':
        setIsStreaming(false)
        setIsTyping(false)
        const errorText = `${message.error}${message.details ? ': ' + message.details : ''}`
        addMessage('error', 'Error', errorText)
        break
    }
  }, [])

  const addMessage = useCallback((type: MessageProps['type'], character: string, text: string, timestamp?: string) => {
    const newMessage: MessageProps = {
      type,
      character,
      text,
      timestamp
    }
    setMessages(prev => [...prev, newMessage])
  }, [])

  const sendMessage = useCallback((text: string) => {
    if (!ws || ws.readyState !== WebSocket.OPEN) return
    
    const now = new Date()
    const timestamp = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    
    // Add user message to UI
    addMessage('user', 'You', text, timestamp)
    
    // Send to server
    if (isStreaming) {
      ws.send(JSON.stringify({ type: 'interrupt', text }))
    } else {
      ws.send(JSON.stringify({ type: 'chat', text }))
    }
    
    setIsTyping(true)
  }, [ws, isStreaming, addMessage])



  useEffect(() => {
    connect()
    
    return () => {
      if (ws) {
        ws.close()
      }
    }
  }, [connect])

  const value: ChatContextType = {
    messages,
    skills,
    connectionStatus,
    isConnected,
    isStreaming,
    isTyping,
    sendMessage
  }

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  )
}
