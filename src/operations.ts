import { DBOS, WorkflowQueue } from '@dbos-inc/dbos-sdk';
import { LLM } from './llm';
import * as fs from 'fs/promises';
import * as path from 'path';

// Interface definitions remain the same...
interface Conversation {
    id: number;
    created_at: Date;
    updated_at: Date;
    theme?: string;
    skills?: string[];
}

interface Message {
    id: number;
    character: string;
    text: string;
    conversation_id: number;
    created_at: Date;
    updated_at: Date;
}

interface DelayedMessage {
    character: string;
    text: string;
    delay: number;
}

// Create a queue for background character thoughts
const characterQueue = new WorkflowQueue("character_queue", { 
    concurrency: 3,
    rateLimit: { limitPerPeriod: 10, periodSec: 60 }
});

export class ForaChat {

    @DBOS.transaction()
    static async createConversation(): Promise<Conversation> {
        const [conversation] = await DBOS.knexClient<Conversation>('forachat.conversations').insert({}).returning('*');
        return conversation;
    }

    @DBOS.transaction()
    static async addMessage(character: string, text: string, conversation_id: number): Promise<Message> {
        const [message] = await DBOS.knexClient<Message>('forachat.messages').insert({
            character,
            text,
            conversation_id
        }).returning('*');
        return message;
    }

    @DBOS.transaction()
    static async updateConversationMetadata(conversationId: number, theme?: string, skills?: string[]): Promise<Conversation> {
        const updateData: any = {
            theme,
            updated_at: new Date()
        };

        if (skills) {
            updateData.skills = JSON.stringify(skills);
        }

        const [conversation] = await DBOS.knexClient<Conversation>('forachat.conversations')
            .where('id', conversationId)
            .update(updateData)
            .returning('*');
        return conversation;
    }

    @DBOS.transaction()
    static async getConversation(conversationId: number): Promise<Conversation | null> {
        const conversation = await DBOS.knexClient<Conversation>('forachat.conversations')
            .where('id', conversationId)
            .first();

        if (conversation && conversation.skills && typeof conversation.skills === 'string') {
            // Parse skills JSON if it's stored as string
            conversation.skills = JSON.parse(conversation.skills as string);
        }

        return conversation || null;
    }

    @DBOS.transaction({ readOnly: true })
    static async getDelayedThoughts(conversationId: number, afterMessageId?: number): Promise<Message[]> {
        let query = DBOS.knexClient<Message>('forachat.messages')
            .where('conversation_id', conversationId)
            .orderBy('id', 'asc');

        if (afterMessageId) {
            query = query.where('id', '>', afterMessageId);
        }

        return query;
    }

    @DBOS.transaction({ readOnly: true })
    static async getMessageCount(conversationId: number): Promise<number> {
        const result = await DBOS.knexClient<Message>('forachat.messages')
            .where('conversation_id', conversationId)
            .count('id as count')
            .first() as { count: string } | undefined;

        return parseInt(result?.count || '0') || 0;
    }

    @DBOS.step()
    static async getSystemPrompt(characterName?: string): Promise<string> {
        // Determine which system file to load based on character name
        let systemFileName = 'agent_system.md';
        if (characterName) {
            systemFileName = `${characterName.toLowerCase()}_system.md`;
        }

        const filePath = path.join(__dirname, '..', 'prompts', systemFileName);
        let content = await fs.readFile(filePath, 'utf-8');

        // Parse markdown links and incorporate referenced files
        const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
        let match;

        while ((match = linkRegex.exec(content)) !== null) {
            const [fullMatch, linkText, linkedFile] = match;

            try {
                // Resolve the linked file path relative to the prompts directory
                const linkedFilePath = path.join(__dirname, '..', 'prompts', linkedFile);
                const linkedContent = await fs.readFile(linkedFilePath, 'utf-8');

                // Replace the markdown link with the actual content
                // Keep the link text as a header for context
                const replacement = `### ${linkText}\n\n${linkedContent}`;
                content = content.replace(fullMatch, replacement);
            } catch (error) {
                DBOS.logger.warn(`Could not read linked file ${linkedFile}: ${error}`);
                // Keep the original link if file cannot be read
            }
        }

        return content;
    }

    @DBOS.workflow()
    static async characterThoughtWorkflow(
        conversationId: number,
        context: string,
        character: string
    ): Promise<any> {
        // Get message count for logging context
        const messageCount = await ForaChat.getMessageCount(conversationId);

        // Get conversation metadata (theme and skills)
        const conversation = await ForaChat.getConversation(conversationId);

        // Use existing getSystemPrompt to load character-specific prompt
        const characterPrompt = await ForaChat.getSystemPrompt(character);

        // Log conversation context before LLM call
        DBOS.logger.info(`=== CHARACTER THOUGHT WORKFLOW CONTEXT ===`);
        DBOS.logger.info(`Conversation ID: ${conversationId}`);
        DBOS.logger.info(`Message Count in Context: ${messageCount}`);
        DBOS.logger.info(`Character: ${character}`);
        DBOS.logger.info(`Theme: ${conversation?.theme || 'Not set'}`);
        DBOS.logger.info(`Skills: ${conversation?.skills ? JSON.stringify(conversation.skills) : 'Not set'}`);

        // Build enhanced context with theme and skills
        let enhancedContext = context;
        if (conversation?.theme || conversation?.skills) {
            enhancedContext = `Conversation Context:
${conversation.theme ? `Theme: ${conversation.theme}` : ''}
${conversation.skills ? `Skills: ${conversation.skills.join(', ')}` : ''}

${context}`;
        }

        // Generate a delayed thought from this character
        const llmResponse = await LLM.generate(
            characterPrompt,
            `Based on this conversation context, generate a delayed follow-up thought:\n${enhancedContext}`,
            conversationId,
            character
        );

        if (llmResponse && llmResponse.reply && Array.isArray(llmResponse.reply) && llmResponse.reply.length > 0) {
            const responseMessage = llmResponse.reply[0];
            await ForaChat.addMessage(character, responseMessage.text, conversationId);
            return { character, text: responseMessage.text };
        }
        return null;
    }

    @DBOS.workflow()
    static async chatWorkflow(userMessage: string, conversationId?: number): Promise<any> {
        let conversation;
        if (conversationId) {
            // Use existing conversation
            conversation = { id: conversationId };
        } else {
            // Create new conversation
            conversation = await ForaChat.createConversation();
        }

        await ForaChat.addMessage("user", userMessage, conversation.id);

        // Get message count for logging context
        const messageCount = await ForaChat.getMessageCount(conversation.id);

        const systemPrompt = await ForaChat.getSystemPrompt();

        // Log conversation context before LLM call
        DBOS.logger.info(`=== CHAT WORKFLOW CONTEXT ===`);
        DBOS.logger.info(`Conversation ID: ${conversation.id}`);
        DBOS.logger.info(`Message Count in Context: ${messageCount}`);
        DBOS.logger.info(`Character: default (main chat)`);

        const llmResponse = await LLM.generate(systemPrompt, userMessage, conversation.id, 'default');

        DBOS.logger.info(`LLM Response: ${JSON.stringify(llmResponse)}`);

        if (!llmResponse || !llmResponse.reply || !Array.isArray(llmResponse.reply) || llmResponse.reply.length === 0) {
            DBOS.logger.error(`Invalid LLM response structure: ${JSON.stringify(llmResponse)}`);
            throw new Error("Invalid response from LLM - missing or empty reply array");
        }

        // Store all response messages
        for (const responseMessage of llmResponse.reply) {
            await ForaChat.addMessage(responseMessage.character, responseMessage.text, conversation.id);
        }

        // Store theme and skills if provided in the LLM response
        if (llmResponse.theme || llmResponse.skills) {
            await ForaChat.updateConversationMetadata(
                conversation.id,
                llmResponse.theme,
                llmResponse.skills
            );
        }

        // Queue background thoughts for characters not in the initial response
        const respondedCharacters = new Set(llmResponse.reply.map((msg: DelayedMessage) => msg.character));
        const allCharacters = ['Fora', 'Jan', 'Lou'];
        const conversationContext = `User: ${userMessage}\n` +
            llmResponse.reply.map((msg: DelayedMessage) => `${msg.character}: ${msg.text}`).join('\n');
        
        for (const character of allCharacters) {
            if (!respondedCharacters.has(character)) {
                // Queue a potential delayed response from this character
                await DBOS.startWorkflow(ForaChat, { 
                    queueName: characterQueue.name 
                }).characterThoughtWorkflow(conversation.id, conversationContext, character);
            }
        }

        return { ...llmResponse, conversationId: conversation.id };
    }

    @DBOS.workflow()
    static async interruptedChatWorkflow(
        userMessage: string,
        previousMessages: Array<{character: string, text: string}>,
        conversationId?: number
    ): Promise<any> {
        let conversation;
        if (conversationId) {
            conversation = { id: conversationId };
        } else {
            conversation = await ForaChat.createConversation();
        }

        // Build context from previous messages and new user input
        const contextMessages = previousMessages.map(msg => `${msg.character}: ${msg.text}`).join('\n');
        const fullPrompt = `Previous conversation:\n${contextMessages}\n\nUser (interrupting): ${userMessage}`;

        await ForaChat.addMessage("user", userMessage, conversation.id);

        // Get message count for logging context
        const messageCount = await ForaChat.getMessageCount(conversation.id);

        const systemPrompt = await ForaChat.getSystemPrompt();

        // Log conversation context before LLM call
        DBOS.logger.info(`=== INTERRUPTED CHAT WORKFLOW CONTEXT ===`);
        DBOS.logger.info(`Conversation ID: ${conversation.id}`);
        DBOS.logger.info(`Message Count in Context: ${messageCount}`);
        DBOS.logger.info(`Character: default (interrupted chat)`);
        DBOS.logger.info(`Previous Messages Count: ${previousMessages.length}`);

        const llmResponse = await LLM.generate(systemPrompt, fullPrompt, conversation.id, 'interrupted');

        DBOS.logger.info(`Interrupted LLM Response: ${JSON.stringify(llmResponse)}`);

        if (!llmResponse || !llmResponse.reply || !Array.isArray(llmResponse.reply) || llmResponse.reply.length === 0) {
            DBOS.logger.error(`Invalid LLM response structure: ${JSON.stringify(llmResponse)}`);
            throw new Error("Invalid response from LLM - missing or empty reply array");
        }

        // Store all response messages
        for (const responseMessage of llmResponse.reply) {
            await ForaChat.addMessage(responseMessage.character, responseMessage.text, conversation.id);
        }

        // Store theme and skills if provided in the LLM response
        if (llmResponse.theme || llmResponse.skills) {
            await ForaChat.updateConversationMetadata(
                conversation.id,
                llmResponse.theme,
                llmResponse.skills
            );
        }

        // Queue background thoughts for characters not in the initial response
        const respondedCharacters = new Set(llmResponse.reply.map((msg: DelayedMessage) => msg.character));
        const allCharacters = ['Fora', 'Jan', 'Lou'];
        
        for (const character of allCharacters) {
            if (!respondedCharacters.has(character)) {
                // Queue a potential delayed response from this character
                await DBOS.startWorkflow(ForaChat, { 
                    queueName: characterQueue.name 
                }).characterThoughtWorkflow(conversation.id, fullPrompt, character);
            }
        }

        return { ...llmResponse, conversationId: conversation.id };
    }

    @DBOS.workflow()
    static async determineConversationRelevance(
        userMessage: string, 
        conversationId: number
    ): Promise<boolean> {
        // Get conversation metadata and recent messages
        const conversation = await ForaChat.getConversation(conversationId);
        
        // Build context from recent messages
        const recentMessages = await DBOS.knexClient<Message>('forachat.messages')
            .where('conversation_id', conversationId)
            .orderBy('id', 'desc')
            .limit(5);
        
        const context = recentMessages.reverse().map(msg => `${msg.character}: ${msg.text}`).join('\n');
        
        // Get system prompt
        const systemPrompt = await ForaChat.getSystemPrompt();
        
        // Ask LLM to determine if the message is related to the conversation
        const llmResponse = await LLM.generate(
            systemPrompt,
            `Determine if this user message is related to the ongoing conversation:\n\nConversation context:\n${context}\n\nUser message: ${userMessage}\n\nRespond with a JSON object with a single "isRelated" boolean field.`,
            conversationId,
            'system'
        );
        
        // Extract the determination
        if (llmResponse && typeof llmResponse === 'object' && 'isRelated' in llmResponse) {
            return !!llmResponse.isRelated;
        }
        
        // Default to true if we can't determine
        return true;
    }
}
